<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\model\LiveRoomModel;
use app\model\LiveRoomProductModel;
use app\model\LiveRoomStatsModel;
use app\model\ItineraryModel;
use support\Request;
use support\Response;

/**
 * 直播间管理控制器
 */
class LiveRoomController extends BaseController
{
    /**
     * 获取直播间列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $params = $this->getPaginationParams($request);
            $where = [];

            // 搜索条件
            $title = $request->input('title');
            if ($title) {
                $where[] = ['title', 'like', "%{$title}%"];
            }

            $status = $request->input('status');
            if ($status) {
                $where['status'] = $status;
            }

            $anchorName = $request->input('anchor_name');
            if ($anchorName) {
                $where[] = ['anchor_name', 'like', "%{$anchorName}%"];
            }

            $isFeatured = $request->input('is_featured');
            if ($isFeatured !== null) {
                $where['is_featured'] = (int)$isFeatured;
            }

            // 添加软删除条件
            $where['delete_time'] = 0;

            $model = new LiveRoomModel();
            $result = $model->getList($where, $params['page'], $params['limit']);

            return success($result);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取直播间列表');
        }
    }

    /**
     * 获取直播间详情
     * @param Request $request
     * @return Response
     */
    public function show(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500,'直播间ID不能为空');
            }

            $model = new LiveRoomModel();
            $liveRoom = $model->getDetail($id);

            if (!$liveRoom) {
                return error(500,'直播间不存在');
            }

            // 获取关联的商品信息
            $productModel = new LiveRoomProductModel();
            $products = $productModel->getListByLiveRoom($id, [], 1, 100);
            $liveRoom['products'] = $products['list'];

            return success($liveRoom);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取直播间详情');
        }
    }

    /**
     * 创建直播间
     * @param Request $request
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            $data = $this->getJsonData($request);

            // 验证必需字段
            $required = ['title', 'description'];
            $errors = $this->validateRequired($data, $required);
            if ($errors) {
                return error(400,'参数验证失败', $errors);
            }

            // 设置默认值
            $data['create_time'] = time();
            $data['update_time'] = time();

            $model = new LiveRoomModel();
            $id = $model->insertGetId($data);

            if (!$id) {
                return error(500, '创建直播间失败');
            }

            return success(['id' => $id], '创建直播间成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '创建直播间');
        }
    }

    /**
     * 更新直播间
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500,'直播间ID不能为空');
            }

            $data = $this->getJsonData($request);
            $data['update_time'] = time();
            $model = new LiveRoomModel();
            $result = $model->where('id', $id)->update($data);

            if ($result === false) {
                return error(500,'更新直播间失败');
            }

            return success([], '更新直播间成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '更新直播间');
        }
    }

    /**
     * 删除直播间
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500, '直播间ID不能为空');
            }

            $model = new LiveRoomModel();
            $result = $model->where('id', $id)->update(['delete_time' => time()]);

            if ($result === false) {
                return error(500, '删除直播间失败');
            }

            return success([], '删除直播间成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '删除直播间');
        }
    }

    /**
     * 批量删除直播间
     * @param Request $request
     * @return Response
     */
    public function batchDestroy(Request $request): Response
    {
        try {
            $data = $this->getJsonData($request);
            $ids = $data['ids'] ?? [];

            if (empty($ids) || !is_array($ids)) {
                return error(500, '请选择要删除的直播间');
            }

            $model = new LiveRoomModel();
            $result = $model->whereIn('id', $ids)->update(['delete_time' => time()]);

            if ($result === false) {
                return error(500, '批量删除直播间失败');
            }

            return success([], '批量删除直播间成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '批量删除直播间');
        }
    }

    /**
     * 更新直播间状态
     * @param Request $request
     * @return Response
     */
    public function updateStatus(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(400, '直播间ID不能为空');
            }

            $status = $request->post('status', 0);
            $model = new LiveRoomModel();
            $updateData = ['status' => $status, 'update_time' => time()];
            $result = $model->where('id', $id)->update($updateData);

            if ($result === false) {
                return error(400,'更新状态失败');
            }

            return success([], '更新状态成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '更新直播间状态');
        }
    }

    /**
     * 获取直播间商品列表
     * @param Request $request
     * @return Response
     */
    public function products(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500, '直播间ID不能为空');
            }

            $params = $this->getPaginationParams($request);
            $where = [];

            $status = $request->input('status');
            if ($status) {
                $where['status'] = $status;
            }

            $isFeatured = $request->input('is_featured');
            if ($isFeatured !== null) {
                $where['is_featured'] = (int)$isFeatured;
            }

            $productModel = new LiveRoomProductModel();
            $result = $productModel->getListByLiveRoom((int)$id, $where, $params['page'], $params['limit']);

            return success($result);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取直播间商品列表');
        }
    }

    /**
     * 添加商品到直播间
     * @param Request $request
     * @return Response
     */
    public function addProduct(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500, '直播间ID不能为空');
            }

            $data = $this->getJsonData($request);

            // 验证必需字段
            $required = ['product_id'];
            $errors = $this->validateRequired($data, $required);
            if ($errors) {
                return error(400, '参数验证失败', $errors);
            }

            $productModel = new LiveRoomProductModel();

            // 检查商品是否已存在
            if ($productModel->isProductExists($id, $data['product_id'])) {
                return error(500, '商品已存在于直播间中');
            }

            // 验证商品是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->find($data['product_id']);
            if (!$itinerary) {
                return error(500, '商品不存在');
            }

            $insertData = [
                'live_room_id' => $id,
                'product_id' => $data['product_id'],
                'product_type' => $data['product_type'] ?? 'itinerary',
                'special_price' => $data['special_price'] ?? null,
                'discount_rate' => $data['discount_rate'] ?? null,
                'stock_limit' => $data['stock_limit'] ?? null,
                'is_featured' => $data['is_featured'] ?? 0,
                'sort_order' => $data['sort_order'] ?? 0,
                'status' => $data['status'] ?? LiveRoomProductModel::STATUS_ACTIVE,
                'create_time' => time(),
                'update_time' => time()
            ];

            $result = $productModel->insert($insertData);

            if (!$result) {
                return error(500, '添加商品失败');
            }

            return success([], '添加商品成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '添加商品到直播间');
        }
    }

    /**
     * 批量添加商品到直播间
     * @param Request $request
     * @return Response
     */
    public function batchAddProducts(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500, '直播间ID不能为空');
            }

            $data = $this->getJsonData($request);
            $products = $data['products'] ?? [];

            if (empty($products) || !is_array($products)) {
                return error(500, '请选择要添加的商品');
            }

            $productModel = new LiveRoomProductModel();
            $result = $productModel->batchAddProducts((int)$id, $products);

            if (!$result) {
                return error(500,'批量添加商品失败');
            }

            return success([], '批量添加商品成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '批量添加商品到直播间');
        }
    }

    /**
     * 移除直播间商品
     * @param Request $request
     * @return Response
     */
    public function removeProduct(Request $request, $id, $productId): Response
    {
        try {
            if (!$id || !$productId) {
                return error(500, '参数不能为空');
            }

            $productModel = new LiveRoomProductModel();
            $result = $productModel->where('live_room_id', $id)
                ->where('product_id', $productId)
                ->update(['delete_time' => time()]);

            if ($result === false) {
                return error(500, '移除商品失败');
            }

            return success([], '移除商品成功');
        } catch (\Exception $e) {
            return $this->handleException($e, '移除直播间商品');
        }
    }

    /**
     * 获取直播间统计数据
     * @param Request $request
     * @return Response
     */
    public function stats(Request $request, $id): Response
    {
        try {
            if (!$id) {
                return error(500, '直播间ID不能为空');
            }

            $startDate = $request->input('start_date', '');
            $endDate = $request->input('end_date', '');

            $statsModel = new LiveRoomStatsModel();

            // 获取统计汇总
            $summary = $statsModel->getSummary($id, $startDate, $endDate);

            // 获取趋势数据
            $trendData = [];
            if ($startDate && $endDate) {
                $trendData = $statsModel->getTrendData($id, $startDate, $endDate);
            }

            return success([
                'summary' => $summary,
                'trend_data' => $trendData
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e, '获取直播间统计数据');
        }
    }
}
